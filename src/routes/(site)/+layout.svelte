<script lang="ts">
	import 'tailwindcss/tailwind.css';
	import Nav from '$lib/components/nav.svelte';
	import Sidebar from '$lib/components/sidebar.svelte';
	import Footer from '$lib/components/footer.svelte';
	import type { LayoutData } from './$types';
	import { setUserProfileLanguage } from '$lib/stores/i18n';
	
	export let data: LayoutData;

	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';

	$: ({ role, status } = data);

	onMount(() => {
		if (!data.isLogin) {
			goto('/login'); // Redirect to login page if not logged in
		} else if (data.preferred_language) {
			// Set language from user profile
			setUserProfileLanguage(data.preferred_language as 'en' | 'th');
		}
	});
</script>

<!-- <Nav
	isLogin={data.isLogin}
	id={data.id}
	name_avartar={data.name_avartar}
	fullname={data.fullname}
	email={data.email}
	status={status}
    role={role}
/> -->

<div class="flex h-screen bg-gray-100">
    <!-- Sidebar component handles its own minimizing -->
    <Sidebar
        isLogin={data.isLogin}
        id={data.id}
        name_avartar={data.name_avartar}
        fullname={data.fullname}
        email={data.email}
        status={status}
        role={role}
    />

    <!-- Main Content -->
    <div class="flex-1 overflow-auto">
        <!-- <div class="p-4"> -->
        <div>
            <!-- <div class="container mx-auto mb-10"> -->
            <div class="w-full h-full">
                <slot />
            </div>
        </div>
    </div>
</div>

<Footer />
