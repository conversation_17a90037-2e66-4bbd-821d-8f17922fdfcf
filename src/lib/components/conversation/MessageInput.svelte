<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { t, language } from '$lib/stores/i18n';
	const dispatch = createEventDispatcher();
	
	let messageText = '';
	let isTyping = false;
	let typingTimeout: number | null = null;
	let fileInput: HTMLInputElement;
	let selectedFiles: File[] = [];
	let showFilePreview = false;
	
	function handleSend() {
		if (messageText.trim() || selectedFiles.length > 0) {
			dispatch('send', {
				content: messageText.trim(),
				type: 'TEXT',
				files: selectedFiles
			});
			messageText = '';
			selectedFiles = [];
			showFilePreview = false;
			stopTyping();
		}
	}
	
	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			handleSend();
		}
	}
	
	function handleInput() {
		// Emit typing indicator
		if (!isTyping) {
			isTyping = true;
			dispatch('typing', { isTyping: true });
		}
		
		// Clear existing timeout
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
		}
		
		// Set new timeout to stop typing indicator
		typingTimeout = window.setTimeout(() => {
			stopTyping();
		}, 1000);
	}
	
	function stopTyping() {
		if (isTyping) {
			isTyping = false;
			dispatch('typing', { isTyping: false });
		}
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
			typingTimeout = null;
		}
	}
	
	function handleFileClick() {
		fileInput.click();
	}
	
	function handleFileSelect(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files.length > 0) {
			// เพิ่มไฟล์ใหม่เข้าไปใน array เดิม แทนการ replace
			selectedFiles = [...selectedFiles, ...Array.from(input.files)];
			showFilePreview = true;
			// Reset input เพื่อให้สามารถเลือกไฟล์เดิมซ้ำได้
			input.value = '';
		}
	}
	
	function removeFile(index: number) {
		selectedFiles = selectedFiles.filter((_, i) => i !== index);
		if (selectedFiles.length === 0) {
			showFilePreview = false;
		}
	}
	
	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}
</script>

<!-- File Preview -->
{#if showFilePreview && selectedFiles.length > 0}
	<div class="border-t border-gray-200 bg-gray-50 px-4 py-3">
		<div class="flex items-center justify-between mb-3">
			<span class="text-sm font-medium text-gray-700">ไฟล์ที่จะส่ง:</span>
			<button
				on:click={() => { selectedFiles = []; showFilePreview = false; }}
				class="text-sm text-red-600 hover:text-red-700"
			>
				ยกเลิกทั้งหมด
			</button>
		</div>
		
		<!-- File Stack -->
		<div class="relative">
			{#each selectedFiles as file, index}
				<div 
					class="relative bg-white rounded-lg p-3 border shadow-sm mb-2 last:mb-0"
					style="transform: translateY({index * -2}px) translateX({index * 2}px); z-index: {selectedFiles.length - index};"
				>
					<div class="flex items-center justify-between">
						<div class="flex-1 min-w-0">
							<div class="text-sm font-medium text-gray-900 truncate">{file.name}</div>
							<div class="text-xs text-gray-500">{formatFileSize(file.size)}</div>
						</div>
						<button
							on:click={() => removeFile(index)}
							class="ml-2 text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-50"
							title="ลบไฟล์"
						>
							<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
							</svg>
						</button>
					</div>
				</div>
			{/each}
		</div>
	</div>
{/if}

<!-- Message Input Area -->
<div class="border-t border-gray-200 px-4 py-3 bg-white">
	<div class="flex items-center space-x-3">
		<!-- File Upload Button -->
		<button
			on:click={handleFileClick}
			class="flex-shrink-0 w-10 h-10 flex items-center justify-center hover:bg-gray-100 rounded-full transition-colors"
			title="แนบไฟล์"
		>
			<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
			</svg>
		</button>
		
		<!-- Message Input -->
		<div class="flex-1 min-w-0">
			<textarea
				bind:value={messageText}
				on:keypress={handleKeyPress}
				on:input={handleInput}
				placeholder={t('type_message')}
				rows="1"
				class="w-full resize-none rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
				style="min-height: 40px; max-height: 120px;"
			/>
		</div>
		
		<!-- Send Button -->
		<button
			on:click={handleSend}
			disabled={!messageText.trim() && selectedFiles.length === 0}
			class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full transition-colors {(messageText.trim() || selectedFiles.length > 0)
				? 'bg-blue-500 hover:bg-blue-600 text-white shadow-sm' 
				: 'bg-gray-100 text-gray-400 cursor-not-allowed'}"
			title="ส่งข้อความ"
		>
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
			</svg>
		</button>
	</div>
</div>

<!-- Hidden File Input -->
<input
	bind:this={fileInput}
	type="file"
	multiple
	on:change={handleFileSelect}
	class="hidden"
	accept="*/*"
/>

<style>
	textarea {
		overflow-y: auto;
		line-height: 1.5;
	}
	
	textarea:focus {
		resize: none;
	}
	
	/* Custom scrollbar for file preview */
	.relative {
		min-height: 60px;
	}
</style>