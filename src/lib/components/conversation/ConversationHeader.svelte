<script lang="ts">
	import { DotsVerticalOutline, UserSolid, CaretLeftSolid, CaretRightSolid } from 'flowbite-svelte-icons';
	import { Heading, Button, Dropdown, DropdownItem } from 'flowbite-svelte';
	import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
    import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
    import ChangeTicketPriority from '$lib/components/UI/ChangeTicketPriority.svelte';

	export let customerId: number;
	export let customerName: string;
	export let channelName: string;
	export let connected: boolean = false;
	export let platformId: any = [];
	export let access_token: string;

	// export let ticket: any;
	export let users: any[];
	export let priorities: any[];
	export let statuses: any[];
	export let topics: any[];
	// console.log(topics)

	import { t } from '$lib/stores/i18n';
	import { services } from "$src/lib/api/features";


	import { getPriorityClass, getStatusClass } from '$lib/utils';

	// State variables
	let ticket: any = null;
	let loginUser: any = null;
	let loading = true;
	let error: string | null = null;
	let currentPriorityName: string = ''; // Add this reactive variable

	// Async function to get ticket
	async function getTicket(customerId: number, platformId: any, accessToken: string) {
		try {
			loading = true;
			error = null;
			
			const platformInfo = await services.customers.getPlatformInfo(customerId, platformId, accessToken);
			const backendTicketId = platformInfo.id
			const response_ticket = await services.tickets.getById(backendTicketId, accessToken);
			const response_selfUserInfo = await services.users.getUserInfo(accessToken);
			
			ticket = response_ticket.tickets;
			loginUser = response_selfUserInfo.users;
			
			// Update the reactive variable
			currentPriorityName = ticket?.priority?.name || '';
			
			// console.log('Ticket Information:', ticket)
			// console.log('Ticket Priority', currentPriorityName)
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to fetch ticket';
			console.error('Error fetching ticket:', err);
			ticket = null; // Changed from [] to null for consistency
			loginUser = null;
			currentPriorityName = '';
		} finally {
			loading = false;
		}
	}

	// Reactive function to refetch when dependencies change
	$: if (customerId && platformId && access_token) {
		getTicket(customerId, platformId, access_token);
	}

</script>

<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex items-center justify-between">
		<div>
			<h2 class="text-lg font-semibold text-gray-900">{customerName}</h2>
			<p class="text-sm text-gray-500">{channelName}</p>
		</div>
		
		<div class="flex items-center space-x-4">       

			<!-- Priority -->
			<div class="flex justify-start">
				<span class={`${getStatusClass(ticket?.status_id)} px-3 py-1 rounded-md text-sm w-32 text-center`}>
					{ticket?.status.charAt(0).toUpperCase() + ticket?.status.slice(1)}
				</span>
			</div>

			<div class="flex justify-start">  
				<span class={`${getPriorityClass(currentPriorityName)} p-2 rounded-md text-sm w-24`}>
					{currentPriorityName ?? "-"}
				</span>
			</div> 

			<!-- Connection Status -->
			<div class="flex items-center space-x-2">
				<div class="w-2 h-2 rounded-full {connected ? 'bg-green-500' : 'bg-red-500'}"></div>
				<span class="text-md text-gray-600">
					{connected ? t('connected') : t('disconnected')}
				</span>
			</div>
			
			<!-- Actions -->
			<!-- <button class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
				<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
			</button> -->
			
			<div class="flex items-center justify-end gap-2 pr-4">      
                <Button color="none" class="p-1 ">
                    <DotsVerticalOutline class="h-8 w-8" />
                </Button>
                <Dropdown class="flex flex-col text-left">
                    <TransferTicketOwner
						ticket={ticket}
                        users={users}
                        loggedInUsername={loginUser.username}
                        loggedInRole={loginUser.roles[0].name}
                        isDropDownItem={true}
                    />
                    <ChangeTicketStatus 
						ticket={ticket} 
						statuses={statuses} 
						ticket_topics={topics} 
						isDropDownItem={true} 
					/>
                    <ChangeTicketPriority {ticket} {priorities} isDropDownItem={true} />
                </Dropdown> 
            </div>
		</div>
		
	</div>
</div>