<script lang="ts">
	import { onMount, afterUpdate, createEventDispatcher } from 'svelte';
	import MessageItem from './MessageItem.svelte';
	import LoadingSpinner from '../common/LoadingSpinner.svelte';
	import InfiniteScroll from '../common/InfiniteScroll.svelte';

	import type { Message } from '$lib/types/customer';
	
	export let messages: Message[] = [];
	export let loading: boolean = false;
	
	const dispatch = createEventDispatcher();
	
	let scrollContainer: HTMLElement;
	let shouldScrollToBottom = true;
	let isNearBottom = true;
	
	onMount(() => {
		scrollToBottom();
	});
	
	afterUpdate(() => {
		if (shouldScrollToBottom && isNearBottom) {
			scrollToBottom();
		}
	});
	
	function scrollToBottom() {
		if (scrollContainer) {
			scrollContainer.scrollTop = scrollContainer.scrollHeight;
		}
	}
	
	function handleScroll() {
		if (!scrollContainer) return;
		
		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
		
		// Check if user is near bottom (within 100px)
		isNearBottom = distanceFromBottom < 100;
		
		// Check if scrolled to top for loading more
		if (scrollTop === 0 && messages.length > 0) {
			dispatch('loadMore');
		}
	}
	
	// Group messages by date
	function groupMessagesByDate(messages: Message[]) {
		const groups: { date: string; messages: Message[] }[] = [];
		let currentDate = '';
		
		messages.forEach(msg => {
			const msgDate = new Date(msg.created_on).toLocaleDateString();
			
			if (msgDate !== currentDate) {
				currentDate = msgDate;
				groups.push({
					date: msgDate,
					messages: [msg]
				});
			} else {
				groups[groups.length - 1].messages.push(msg);
			}
		});
		
		return groups;
	}
	
	// Check if should show avatar (first message or different sender)
	function shouldShowAvatar(message: Message, index: number, messages: Message[]) {
		if (index === 0) return true;
		const prevMessage = messages[index - 1];
		return prevMessage.is_self !== message.is_self || 
			   prevMessage.user_name !== message.user_name;
	}
	
	$: messageGroups = groupMessagesByDate(messages);
</script>

<div 
	bind:this={scrollContainer}
	on:scroll={handleScroll}
	class="flex-1 overflow-y-auto custom-scrollbar px-6 py-4 bg-gray-50"
>
	{#if loading && messages.length === 0}
		<div class="flex justify-center items-center h-full">
			<LoadingSpinner />
		</div>
	{:else}
		<!-- Load more indicator at top -->
		{#if loading && messages.length > 0}
			<div class="flex justify-center py-2">
				<LoadingSpinner size="sm" />
			</div>
		{/if}
		
		<!-- Messages grouped by date -->
		{#each messageGroups as group}
			<!-- Date separator -->
			<div class="flex items-center my-4">
				<div class="flex-1 border-t border-gray-200"></div>
				<span class="px-3 text-xs text-gray-500 bg-gray-50">
					{group.date}
				</span>
				<div class="flex-1 border-t border-gray-200"></div>
			</div>
			
			<!-- Messages in group -->
			{#each group.messages as message, index}
				<MessageItem 
					{message}
					showAvatar={shouldShowAvatar(message, index, group.messages)}
				/>
			{/each}
		{/each}
		
		{#if messages.length === 0}
			<div class="text-center text-gray-500 mt-8">
				No messages yet. Start a conversation!
			</div>
		{/if}
	{/if}
</div>

<style>
	/* Smooth scroll behavior */
	.custom-scrollbar {
		scroll-behavior: smooth;
	}
</style>