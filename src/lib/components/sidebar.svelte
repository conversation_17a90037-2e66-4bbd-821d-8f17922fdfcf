<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import { page } from '$app/stores';
    import { 
        Sidebar, 
        SidebarWrapper, 
        SidebarBrand, 
        SidebarItem, 
        SidebarGroup,
        SidebarDropdownWrapper,
        SidebarDropdownItem,
        Avatar,
        Button,
        Tooltip
    } from 'flowbite-svelte';
  
    // Import only the icons that your package definitely has
    import {
        ChevronDownOutline, 
        ChevronUpOutline, 
        ChevronRightOutline, 
        ChevronLeftOutline
    } from 'flowbite-svelte-icons';
  
    import LanguageToggle from './LanguageToggle.svelte';
    import { t } from '$lib/stores/i18n';
  
    export let isLogin: boolean;
    export let id: number;
    export let name_avartar: string;
    export let fullname: string;
    export let email: string;
    export let status: string;
    export let role: string;
  
    // Navbar styling state
    let dominantColor = '#ffffff';
    let secondaryColor = '#ffffff';
    let accentColor = '#ffffff';
    let logoCompany = ''; 
    let companyName = 'Loading...';
  
    // Sidebar state
    let isMinimized = false;
    // Track previous minimized state for dropdown toggle
    let wasMinimized = false;
    
    function toggleSidebar() {
        isMinimized = !isMinimized;
        // whenever we toggle…
        if (isMinimized) {
        clearSidebarAutoMinimize();
        } else {
        scheduleSidebarAutoMinimize();
        }
    }
  
    // Slide-up menu state
    let isMenuOpen = false;
    // Animation states
    let menuAnimationState = 'closed'; // 'closed', 'opening', 'open', 'closing'
    // Auto-minimize on inactivity
    let sidebarAutoMinTimeout: ReturnType<typeof setTimeout>;
  
    // schedule a 10s auto-minimize when sidebar is open
    function scheduleSidebarAutoMinimize() {
        clearTimeout(sidebarAutoMinTimeout);
        if (!isMinimized) {
        sidebarAutoMinTimeout = setTimeout(() => {
            if (!isMinimized) toggleSidebar();
        }, 10_000);
        }
    }
  
    // clear whenever we collapse or on any user interaction
    function clearSidebarAutoMinimize() {
        clearTimeout(sidebarAutoMinTimeout);
    }
  
    // wire up global activity listeners
    onMount(() => {
        const reset = () => scheduleSidebarAutoMinimize();
        document.addEventListener('mousemove', reset);
        document.addEventListener('keydown'  , reset);
        document.addEventListener('click'    , reset);
  
        // if starting expanded, kick off the timer
        scheduleSidebarAutoMinimize();
  
        return () => {
        document.removeEventListener('mousemove', reset);
        document.removeEventListener('keydown'  , reset);
        document.removeEventListener('click'    , reset);
        clearSidebarAutoMinimize();
        };
    });
  
    // auto-close timer handle
    let autoCloseTimeout: ReturnType<typeof setTimeout>;
  
    function toggleMenu() {
      if (isMenuOpen) {
        clearTimeout(autoCloseTimeout);
        // Start closing animation
        menuAnimationState = 'closing';
        // After animation completes, set the menu to closed
        setTimeout(() => {
          isMenuOpen = false;
          menuAnimationState = 'closed';
        }, 300); // Match this with the CSS animation duration
      } else {
        isMenuOpen = true;
        menuAnimationState = 'opening';
        isMinimized = false; // Always expand sidebar when opening menu
        // After animation completes, set the state to open
        setTimeout(() => {
          menuAnimationState = 'open';
        }, 300);
  
        // schedule auto-close in 10s if still open
        autoCloseTimeout = setTimeout(() => {
          if (isMenuOpen) toggleMenu();
        }, 5_000);
  
      }
    }
  
    // Close menu when clicking outside
    function handleClickOutside(event) {
      const menu = document.getElementById('slide-up-menu');
      const avatar = document.getElementById('avatar-trigger');
      
      if (isMenuOpen && menu && avatar && !menu.contains(event.target) && !avatar.contains(event.target)) {
        clearTimeout(autoCloseTimeout);
        toggleMenu(); // Use the toggle function to get animation effect
      }
    }
  
    // Cache expiration check
    function isCacheExpired(timestamp) {
      // Cache expires after 1 hour (3600000 ms)
      const expirationTime = 3600000; 
      return Date.now() - timestamp > expirationTime;
    }
  
    onMount(() => {
      // Add event listener for closing menu
      document.addEventListener('click', handleClickOutside);
      
      // Load settings
      const cached = localStorage.getItem('app_settings');
      if (cached) {
        try {
          const cachedData = JSON.parse(cached);
          
          // Check if cache is expired or if dominantColor has changed
          if (isCacheExpired(cachedData.ts)) {
            console.log("Cache expired, fetching fresh settings");
            fetchSettings(true);
          } else {
            // Apply cached settings
            console.log("Using cached settings");
            applySettings(cachedData.data);
            
            // Still fetch in background to check for updates
            fetchSettings(false);
          }
        } catch (err) {
          console.error("Error parsing cached settings:", err);
          fetchSettings(true);
        }
      } else {
        fetchSettings(true);
      }
      
      // Clean up event listener on component destroy
      return () => {
        document.removeEventListener('click', handleClickOutside);
      };
    });
  
    function applySettings(s: any) {
      // Store old values to check for changes
      const oldDominantColor = dominantColor;
      
      // Apply new settings
      dominantColor = s.DOMINANT_COLOR;
      secondaryColor = s.SECONDARY_COLOR;
      accentColor = s.ACCENT_COLOR;
      logoCompany = s.COMPANY_LOGO;
      companyName = s.COMPANY_ENGLISH_NAME;
    }
  
    async function fetchSettings(forceUpdate: boolean = false): Promise<void> {
        try {
            // Add cache-busting parameter if forcing an update
            const queryParam = forceUpdate ? `?t=${Date.now()}` : '';
            const res = await fetch(`/api/settings/${queryParam}`);
            const json = await res.json();
            const s = json.system_settings;
            
            // Check if the settings are different from what we have
            let hasChanges = false;
            if (s.DOMINANT_COLOR !== dominantColor || 
                s.SECONDARY_COLOR !== secondaryColor || 
                s.ACCENT_COLOR !== accentColor || 
                s.COMPANY_LOGO !== logoCompany || 
                s.COMPANY_ENGLISH_NAME !== companyName) {
                hasChanges = true;
            }
            
            if (hasChanges || forceUpdate) {
                console.log("Applying new settings from API");
                applySettings(s);
                
                // Update cache with new settings
                localStorage.setItem(
                    'app_settings',
                    JSON.stringify({ data: s, ts: Date.now() })
                );
            } else {
                console.log("No changes detected in settings");
            }
        } catch (err) {
            console.error('Error fetching settings:', err);
        }
    }
  
    // Status logic
    let userStatus = status;
    // const statusOptions = [
    //   { id: 'online', label: 'Online', color: 'text-green-500', dotColor: 'bg-green-500' },
    //   { id: 'away',   label: 'Away',   color: 'text-yellow-500', dotColor: 'bg-yellow-500' }
    // ];
    $: statusOptions = [
        { id: 'online', label: t('online'), dotColor: 'bg-green-500' },
        { id: 'away',   label: t('away'),   dotColor: 'bg-yellow-500' }
    ];
  
    async function setStatus(s: string) {
      try {
        const res = await fetch(`/api/users/update_my_status?status=${s}`);
        const result = await res.json();
        if (result.message === 'ok') {
          userStatus = result.new_status;
          toggleMenu(); // Close with animation
        } else {
          alert('Changing Status failed. Please try again.');
        }
      } catch {
        alert('Changing Status failed. Please try again.');
      }
    }
  
    // Logout
    async function logout() {
      try {
        const res = await fetch('/api/logout');
        const result = await res.json();
        if (result.message === 'ok') {
          // Clear language preference from localStorage
          if (result.clearLanguage) {
            localStorage.removeItem('lang');
          }
          window.location.href = '/';
        } else {
          alert('Logout failed. Please try again.');
        }
      } catch {
        alert('Logout failed. Please try again.');
      }
    }
  
    // Nav helpers
    $: activeUrl = $page.url.pathname.split('/', 2).join('/');
    let spanClass = 'flex-1 ms-3 whitespace-nowrap';
    
    // Only use the reactive declaration for site
    $: site = {
      name: companyName,
      href: '/',
      img: logoCompany
    };
  
    // Settings dropdown state
    let isSettingsOpen = false;
    
    function toggleSettings() {
      // If sidebar is minimized, expand it first and then open the dropdown
      if (isMinimized) {
        wasMinimized = true;
        isMinimized = false;
        // Set timeout to ensure sidebar expands first
        setTimeout(() => {
          isSettingsOpen = !isSettingsOpen;
        }, 10);
      } else {
        // Regular toggle if sidebar is already expanded
        isSettingsOpen = !isSettingsOpen;
      }
    }
  
    // Knowledge Base dropdown state
    let isKnowledgeBaseOpen = false;
    
    function toggleKnowledgeBase() {
      // If sidebar is minimized, expand it first and then open the dropdown
      if (isMinimized) {
        wasMinimized = true;
        isMinimized = false;
        // Set timeout to ensure sidebar expands first
        setTimeout(() => {
          isKnowledgeBaseOpen = !isKnowledgeBaseOpen;
        }, 10);
      } else {
        // Regular toggle if sidebar is already expanded
        isKnowledgeBaseOpen = !isKnowledgeBaseOpen;
      }
    }
  
    // Handle clicking outside dropdowns to close them and restore minimized state
    function handleClickOutsideDropdowns(event) {
      const knowledgeBaseTrigger = document.getElementById('menu-item-knowledge-base');
      const settingsTrigger = document.getElementById('menu-item-settings');
      
      // Check if click is outside both triggers
      if (
        (isKnowledgeBaseOpen || isSettingsOpen) && 
        wasMinimized && 
        (!knowledgeBaseTrigger?.contains(event.target) && !settingsTrigger?.contains(event.target))
      ) {
        // Close the dropdowns and restore minimized state
        isKnowledgeBaseOpen = false;
        isSettingsOpen = false;
        
        // Set a small timeout to make the transition smoother
        setTimeout(() => {
          isMinimized = true;
          wasMinimized = false;
        }, 200);
      }
    }
    
    // Add event listener for handling outside clicks on dropdowns
    onMount(() => {
      document.addEventListener('click', handleClickOutsideDropdowns);
      
      return () => {
        document.removeEventListener('click', handleClickOutsideDropdowns);
      };
    });
  
    // Menu items - arranged in the specified order with Testing moved to Knowledge Base dropdown
    const menuItems = [
        // {
        //     label: 'Home', 
        //     key: 'home', 
        //     href: '/', 
        //     icon: '/navbar/home.svg',
        //     activeCheck: '/home'
        // },
        { 
            label: 'Chat_Center', 
            key: 'chat_center',
            href: '/chat_center', 
            icon: '/navbar/ticket.svg',
            activeCheck: '/chat_center'
        },
        { 
            label: 'Dashboard', 
            key: 'dashboard', 
            href: '/dashboard', 
            icon: '/navbar/dashboard.svg',
            activeCheck: '/dashboard'
        },
        { 
            label: 'Tickets', 
            key: 'tickets',
            href: '/monitoring', 
            icon: '/navbar/ticket.svg',
            activeCheck: '/monitoring'
        },
        { 
            label: 'Users', 
            key: 'users',
            href: '/users', 
            icon: '/navbar/users.svg',
            activeCheck: '/users'
        },
        { 
            label: 'Customers', 
            key: 'customers',
            href: '/customer', 
            icon: '/navbar/customers.svg',
            activeCheck: '/customer'
        }
    ];
    
    // Knowledge Base submenu items
    const knowledgeBaseItems = [
        {
            label: 'Upload Files',
            key: 'uploadFiles',
            href: '/knowledge',
            icon: '/navbar/upload-files.svg',
            activeCheck: '/knowledge'
        },
        {
            label: 'Testing',
            key: 'testing',
            href: '/llm_testing',
            icon: '/navbar/testing.svg',
            activeCheck: '/llm_testing'
        }
    ];
    
    // Settings submenu items
    const settingsItems = [
        {
            label: 'Business',
            key: 'business',
            href: '/settings/business',
            icon: '/navbar/general.svg',
            activeCheck: '/settings/business',
            roles: ['Admin', 'Supervisor']
        },
        {
            label: 'Team management',
            key: 'teamManagement',
            href: '/settings/team',
            icon: '/navbar/team-management.svg',
            activeCheck: '/settings/team',
            roles: ['Admin', 'Supervisor']
        },
        {
            label: 'Account',
            key: 'account',
            href: '/settings/account',
            icon: '/navbar/personal.svg',
            activeCheck: '/settings/account',
            roles: ['Admin', 'Supervisor', 'Agent']
        }
    ];
  
    // Does the current role have anything to show in Settings?
    $: hasSettings = settingsItems.some(i => !i.roles || i.roles.includes(role));
  
    // New function for tasks
    function navigateToTasks() {
      goto(`/users/${id}`);
      toggleMenu(); // Close with animation
    }

    function getIconUrl(path: string) {
        return `url(${path})`;
    }
  </script>
  
  <div class={`h-screen flex flex-col transition-all duration-300 ${isMinimized ? 'w-20' : 'w-56'}`} 
     style="background-color: {dominantColor};">
  
    <!-- Logo Section -->
    <div class="p-4 flex items-center justify-between">
        {#if !isMinimized}
        <div class="flex items-center flex-1 min-w-0 mr-2">
            {#if site.img !== ''}
                <img src={site.img} class="h-8 mr-3 flex-shrink-0" alt="Company Logo" />
            {/if}
              <span class="text-white font-semibold truncate max-w-[calc(100%-36px)]">{site.name}</span>
        </div>
        {:else}
          <img src={site.img} class="h-8 mx-auto" alt="Company Logo" />
        {/if}
        <button class="text-white p-1 rounded hover:bg-opacity-20 hover:bg-white cursor-pointer flex-shrink-0" on:click={toggleSidebar}>
        {#if isMinimized}
            <ChevronRightOutline class="h-5 w-5" />
        {:else}
            <ChevronLeftOutline class="h-5 w-5" />
        {/if}
        </button>
    </div>
  
  <!-- Menu Items -->
  <div class="flex-1 py-4 overflow-y-auto">
    <ul>
        {#each menuItems as item}
            {#if !item.roleRequired || role === item.roleRequired}
                <li>
                <!-- Regular menu item with tooltip for minimized mode -->
                <a href={item.href} 
                    id={`menu-item-${item.label.toLowerCase().replace(/\s+/g, '-')}`}
                    class={`flex items-center p-3 ${isMinimized ? 'justify-center' : 'px-4'} 
                            ${activeUrl === item.activeCheck ? 'bg-opacity-25 bg-white relative' : 'hover:bg-opacity-10 hover:bg-white'} 
                            transition-all duration-200 cursor-pointer`}>
                    <!-- Active indicator bar -->
                    {#if activeUrl === item.activeCheck}
                    <div class="absolute left-0 top-0 bottom-0 w-1" style="background-color: {secondaryColor};"></div>
                    {/if}
                    <span class={`flex justify-center items-center w-6 h-6 text-2xl ${activeUrl === item.activeCheck ? 'text-white' : 'text-gray-200'}`}>
                      <div class="nav-icon w-7 h-7" style="--icon-url: {getIconUrl(item.icon)}; --icon-color: {accentColor}"></div>
                    </span>
                    {#if !isMinimized}
                    <span class={`ml-3 ${activeUrl === item.activeCheck ? 'font-bold text-white' : 'text-gray-200'} truncate`}>
                        <!-- {item.label} -->
                        {t(item.key)}
                    </span>
                    {/if}
                </a>
                
                <!-- Tooltip for minimized mode -->
                {#if isMinimized}
                    <Tooltip triggeredBy={`#menu-item-${item.label.toLowerCase().replace(/\s+/g, '-')}`} placement="right">
                    <!-- {item.label} -->
                        {t(item.key)}
                    </Tooltip>
                {/if}
                </li>
            {/if}
        {/each}
      
    <!-- Knowledge Base menu with dropdown -->
    <li>
        <!-- Knowledge Base dropdown trigger -->
        <div 
          id="menu-item-knowledge-base"
          on:click={toggleKnowledgeBase}
          class={`flex items-center p-3 ${isMinimized ? 'justify-center' : 'px-4'} 
                 ${activeUrl === '/knowledge' || activeUrl === '/llm_testing' ? 'bg-opacity-25 bg-white relative' : 'hover:bg-opacity-10 hover:bg-white'} 
                 transition-all duration-200 cursor-pointer`}>
          <!-- Active indicator bar -->
          {#if activeUrl === '/knowledge' || activeUrl === '/llm_testing'}
            <div class="absolute left-0 top-0 bottom-0 w-1" style="background-color: {secondaryColor};"></div>
          {/if}
          <span class={`flex justify-center items-center w-6 h-6 text-2xl ${activeUrl === '/knowledge' || activeUrl === '/llm_testing' ? 'text-white' : 'text-gray-200'}`}>
            <div class="nav-icon w-7 h-7" style="--icon-url: {getIconUrl('/navbar/knowledge-base.svg')}; --icon-color: {accentColor}"></div>
          </span>
          {#if !isMinimized}
            <span class={`ml-3 ${activeUrl === '/knowledge' || activeUrl === '/llm_testing' ? 'font-bold text-white' : 'text-gray-200'} truncate`}>
                {t('knowledgeBase')}
            </span>
            <span class="ml-auto">
              {#if isKnowledgeBaseOpen}
                <ChevronUpOutline class="h-4 w-4 text-white" />
              {:else}
                <ChevronDownOutline class="h-4 w-4 text-white" />
              {/if}
            </span>
          {/if}
        </div>
        
        <!-- Tooltip for minimized mode -->
        {#if isMinimized}
          <Tooltip triggeredBy="#menu-item-knowledge-base" placement="right">
            {t('knowledgeBase')}
          </Tooltip>
        {/if}
        
        <!-- Knowledge Base dropdown items -->
        {#if isKnowledgeBaseOpen && !isMinimized}
          <ul class="py-1 space-y-1">
            {#each knowledgeBaseItems as subItem}
              <li>
                <a href={subItem.href}
                   class={`flex items-center pl-9 py-2 pr-4
                          ${activeUrl === subItem.activeCheck ? 'bg-opacity-25 bg-white relative' : 'hover:bg-opacity-10 hover:bg-white'} 
                          transition-all duration-200 cursor-pointer`}>
                  <!-- Active indicator bar -->
                  {#if activeUrl === subItem.activeCheck}
                    <div class="absolute left-0 top-0 bottom-0 w-1" style="background-color: {secondaryColor};"></div>
                  {/if}
                  <span class={`flex justify-center items-center w-5 h-5 text-lg ${activeUrl === subItem.activeCheck ? 'text-white' : 'text-gray-200'}`}>
                    <div class="nav-icon w-7 h-7" style="--icon-url: {getIconUrl(subItem.icon)}; --icon-color: {accentColor}"></div>
                  </span>
                  <span class={`ml-2 ${activeUrl === subItem.activeCheck ? 'font-bold text-white' : 'text-gray-200'} truncate`}>
                    <!-- {subItem.label} -->
                    {t(subItem.key)}
                  </span>
                </a>
              </li>
            {/each}
          </ul>
        {/if}
      </li>
      
      <!-- Settings menu with dropdown -->
      <!-- {#if !role || role === 'Admin'} -->
      {#if hasSettings}
  
        <li>
          <!-- Settings dropdown trigger -->
          <div 
            id="menu-item-settings"
            on:click={toggleSettings}
            class={`flex items-center p-3 ${isMinimized ? 'justify-center' : 'px-4'} 
                   ${activeUrl === '/settings' ? 'bg-opacity-25 bg-white relative' : 'hover:bg-opacity-10 hover:bg-white'} 
                   transition-all duration-200 cursor-pointer`}
            >
            <!-- Active indicator bar -->
            {#if activeUrl === '/settings'}
              <div class="absolute left-0 top-0 bottom-0 w-1" style="background-color: {secondaryColor};"></div>
            {/if}
            <span class={`flex justify-center items-center w-6 h-6 text-2xl ${activeUrl === '/settings' ? 'text-white' : 'text-gray-200'}`}>
              <div class="nav-icon w-7 h-7" style="--icon-url: {getIconUrl('/navbar/settings.svg')}; --icon-color: {accentColor}"></div>
            </span>
            {#if !isMinimized}
              <span class={`ml-3 ${activeUrl === '/settings' ? 'font-bold text-white' : 'text-gray-200'} truncate`}>
                {t('settings')} 
              </span>
              <span class="ml-auto">
                {#if isSettingsOpen}
                  <ChevronUpOutline class="h-4 w-4 text-white" />
                {:else}
                  <ChevronDownOutline class="h-4 w-4 text-white" />
                {/if}
              </span>
            {/if}
          </div>
          
          <!-- Tooltip for minimized mode -->
          {#if isMinimized}
            <Tooltip triggeredBy="#menu-item-settings" placement="right">
                <!-- Settings -->
                {t('settings')}
            </Tooltip>
          {/if}
          
          <!-- Settings dropdown items -->
          {#if isSettingsOpen && !isMinimized}
            <ul class="py-1 space-y-1">
                {#each settingsItems as subItem}
                    {#if !subItem.roles || subItem.roles.includes(role)}
                        <li>
                            <a href={subItem.href}
                                class={`flex items-center pl-9 py-2 pr-4
                                        ${activeUrl === subItem.activeCheck ? 'bg-opacity-25 bg-white relative' : 'hover:bg-opacity-10 hover:bg-white'} 
                                        transition-all duration-200 cursor-pointer`}>
                                <!-- Active indicator bar -->
                                {#if activeUrl === subItem.activeCheck}
                                <div class="absolute left-0 top-0 bottom-0 w-1" style="background-color: {secondaryColor};"></div>
                                {/if}
                                <span class={`flex justify-center items-center w-5 h-5 text-lg ${activeUrl === subItem.activeCheck ? 'text-white' : 'text-gray-200'}`}>
                                    <div class="nav-icon w-7 h-7" style="--icon-url: {getIconUrl(subItem.icon)}; --icon-color: {accentColor}"></div>
                                </span>
                                <span class={`ml-2 ${activeUrl === subItem.activeCheck ? 'font-bold text-white' : 'text-gray-200'} truncate`}>
                                <!-- {subItem.label} -->
                                {t(subItem.key)}
                                </span>
                            </a>
                        </li>
                    {/if}
                {/each}
                </ul>
          {/if}
        </li>
      {/if}
    </ul>
  </div>
  
  <!-- User Profile with Slide-up Menu -->
  {#if isLogin}
    <div class="relative">
      <div class={`p-4 border-t border-gray-700 ${isMinimized ? 'flex justify-center' : ''}`}>
        <div id="avatar-trigger" on:click|stopPropagation={toggleMenu} class={`${!isMinimized ? 'flex items-center cursor-pointer hover:bg-white hover:bg-opacity-10 p-2 rounded transition-all' : 'cursor-pointer hover:bg-white hover:bg-opacity-10 p-2 rounded-full transition-all'}`}>
          <Avatar>{name_avartar}</Avatar>
          {#if !isMinimized}
        <div class="ms-3 min-w-0 flex-1">
              <p class="text-sm font-medium text-white truncate">{fullname}</p>
              <div class="flex items-center">
                <span class={`h-2 w-2 rounded-full ${statusOptions.find(o => o.id === userStatus)?.dotColor} mr-1`}></span>
                <span class="text-xs text-gray-300">{t(userStatus)}</span>
              </div>
            </div>
          {/if}
        </div>
        
        <!-- Tooltip for avatar in minimized mode -->
        {#if isMinimized}
          <Tooltip triggeredBy="#avatar-trigger" placement="right">
            <!-- {fullname} ({userStatus.charAt(0).toUpperCase() + userStatus.slice(1)}) -->
             {fullname} ({t(userStatus)})
          </Tooltip>
        {/if}
      </div>
      
      <!-- Slide-up Menu -->
      {#if isMenuOpen}
        <div id="slide-up-menu" 
            class={`absolute bottom-full left-0 right-0 w-full bg-white rounded-t-lg shadow-lg max-h-[60vh] overflow-hidden transition-all duration-300 ${menuAnimationState === 'opening' || menuAnimationState === 'open' ? 'slide-up-active' : 'slide-up-closing'}`}>
        
  
            <!-- User Info Header -->
            <div class="p-4 border-b flex items-center">
              <Avatar size="md">{name_avartar}</Avatar>
              <div class="ms-3">
                <p class="text-lg font-medium">{fullname}</p>
                <p class="text-sm text-gray-500">{email}</p>
              </div>
            </div>
            
            <!-- Status Options -->
            <div class="p-4 border-b">
                <p class="text-sm text-gray-500 mb-2">
                    <!-- Status -->
                    {t('status')}
                </p>
                {#each statusOptions as opt}
                    <button 
                    on:click={() => setStatus(opt.id)} 
                    class={`flex items-center w-full text-left mb-2 p-2 rounded hover:bg-gray-100 ${userStatus === opt.id ? 'bg-gray-100' : ''}`}>
                        <span class={`h-3 w-3 rounded-full ${opt.dotColor} mr-3`}></span>
                        <span class="text-sm">{t(opt.id)}</span>
                    </button>
                {/each}
            </div>
            <!-- <div class="p-4 border-b">
                <p class="text-sm text-gray-500 mb-2">
                    {t('language')}
                </p>
                <LanguageToggle />
            </div> -->
            <!-- Actions -->
            <div class="p-4">
                <button 
                    on:click={navigateToTasks}
                    class="flex items-center w-full text-left p-2 rounded hover:bg-gray-100 mb-2">
                    <span class="text-gray-800 mr-3">
                      <div class="nav-icon w-4 h-4" style="--icon-url: {getIconUrl('/navbar/my-tasks.svg')}; --icon-color: #000000"></div>
                    </span>
                    <span>{t('tasks')}</span>
                </button>
  
                <button on:click={logout} class="flex items-center w-full text-left p-2 rounded hover:bg-gray-100 text-red-500">
                    <span class="mr-3">
                      <div class="nav-icon w-4 h-4" style="--icon-url: {getIconUrl('/navbar/logout.svg')}; --icon-color: #000000"></div>
                    </span>
                    <span>{t('logout')}</span>
                </button>
            </div>
        </div>
      {/if}
    </div>
  {:else}
    <div class="p-4 border-t border-gray-700">
      {#if !isMinimized}
        <Button href="/login" class="w-full cursor-pointer">Login</Button>
      {:else}
        <Button id="login-button" href="/login" class="w-full p-2 flex justify-center cursor-pointer">
            <span>🔑</span>
        </Button>
        <Tooltip triggeredBy="#login-button" placement="right">
            Login
        </Tooltip>
      {/if}
    </div>
  {/if}
  </div>
  
  <style>
  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
  
  @keyframes slideDown {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(100%);
    }
  }
  
  .slide-up-active {
    animation: slideUp 0.3s ease-out forwards;
    transform: translateY(0);
  }
  
  .slide-up-closing {
    animation: slideDown 0.3s ease-in forwards;
  }
  
  #slide-up-menu {
    z-index: 1;   
  }
  
  #avatar-trigger {
    position: relative;  
    z-index: 2;
  }
  
  .nav-icon {
  mask-image: var(--icon-url);
  -webkit-mask-image: var(--icon-url);
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-position: center;
  mask-size: contain;
  -webkit-mask-size: contain;
  background-color: var(--icon-color);
  }

  </style>
