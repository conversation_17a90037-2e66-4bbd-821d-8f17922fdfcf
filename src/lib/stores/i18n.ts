// src/stores/i18n.ts
import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import en from '$lib/locales/en.json';
import th from '$lib/locales/th.json';

const dictionaries = { en, th } as const;

/* ------------ language store ------------ */
const getInitialLanguage = (): 'en' | 'th' => {
	if (browser) {
		// Check localStorage for language preference
		const storedLang = localStorage.getItem('lang');
		if (storedLang === 'en' || storedLang === 'th') {
			return storedLang;
		}
	}
	return 'en'; // Default fallback
};

const stored = browser && getInitialLanguage();
export const language = writable<'en' | 'th'>(stored);

// Set user's profile language preference
export function setUserProfileLanguage(lang: 'en' | 'th') {
	if (browser && (lang === 'en' || lang === 'th')) {
		localStorage.setItem('lang', lang);
		language.set(lang);
	}
}

if (browser) {
	language.subscribe((v) => {
		localStorage.setItem('lang', v);
	});
}

/* ------------ active dictionary ------------ */
export const dict = derived(language, ($l) => dictionaries[$l] ?? dictionaries.en);

/* ------------ helper ------------ */
export function t(key: string): string {
	const d = get(dict);
	return (d && (d as Record<string, string>)[key]) ?? key;
}
